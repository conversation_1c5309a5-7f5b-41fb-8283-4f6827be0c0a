using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderDetailConfiguration : IEntityTypeConfiguration<OrderDetail>
{
    public void Configure(EntityTypeBuilder<OrderDetail> builder)
    {
        builder.ToTable("OrderDetails");

        builder.Has<PERSON>ey(od => od.Id);

        builder.Property(od => od.Id)
               .ValueGeneratedNever(); // Assuming ID is a Guid and set manually

        builder.Property(od => od.Description)
               .IsRequired()
               .HasMaxLength(500);

        builder.Property(od => od.Status)
               .IsRequired();

        builder.Property(od => od.Iban)
               .IsRequired();

        builder.Property(od => od.Amount)
               .IsRequired()
               .HasColumnType("decimal(18,2)");

        builder.Property(od => od.WageAmount)
               .IsRequired()
               .HasColumnType("decimal(18,2)");

        builder.HasOne(od => od.Order)
               .WithMany(o => o.OrderDetails)
               .HasForeignKey(od => od.OrderId);
    }
}
