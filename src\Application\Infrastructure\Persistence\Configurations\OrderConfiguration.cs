﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.ToTable("Orders");

        builder.HasKey(o => o.Id);

        builder.Property(o => o.Id)
               .ValueGeneratedNever(); // Assuming Id is a Guid and set manually

        builder.Property(o => o.Description)
               .IsRequired()
               .HasMaxLength(500);

        builder.Property(o => o.Status)
               .IsRequired();

        builder.Property(o => o.ScheduledTime)
               .IsRequired(false);

        builder.HasMany(o => o.OrderDetails)
               .WithOne()
               .HasForeignKey(od => od.OrderId);
    }
}
