﻿using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class OrderDetailRollbackInfo : AuditableEntity
{
    public int Id { get; set; }
    public CorrelationId RollbackTransferTransactionId { get; private set; }
    public WalletId RollbackDestinationWalletId { get; private set; }
    public bool TransactionStatus { get; set; }

    public OrderDetail OrderDetail { get; set; }
    public Guid OrderDetailId { get; set; }

    private OrderDetailRollbackInfo() { }

    public static OrderDetailRollbackInfo Create(Guid rollbackDestinationWalletId, Guid OrderDetailId)
    {
        return new OrderDetailRollbackInfo
        {
            TransactionStatus = false,
            RollbackTransferTransactionId = CorrelationId.New(),
            RollbackDestinationWalletId = WalletId.Of(rollbackDestinationWalletId),
            OrderDetailId = OrderDetailId,
        };
    }
}