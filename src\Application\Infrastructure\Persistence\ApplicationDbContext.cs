﻿using Microsoft.EntityFrameworkCore;
using System.Reflection;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Domain.Outbox;

namespace Zify.Settlement.Application.Infrastructure.Persistence;
public sealed class ApplicationDbContext(
    ICurrentUserService currentUserService,
    IDateTime dateTime,
    DbContextOptions<ApplicationDbContext> options) : DbContext(options)
{
    private DbSet<OutboxEntity> Outboxes { get; set; } = null!;

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        foreach (var entry in ChangeTracker.Entries<AuditableEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = currentUserService.UserId;
                    entry.Entity.Created = dateTime.Now;
                    break;
                case EntityState.Modified:
                    entry.Entity.LastModifiedBy = currentUserService.UserId;
                    entry.Entity.LastModified = dateTime.Now;
                    break;
                case EntityState.Deleted:
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedOn = dateTime.Now;
                    entry.State = EntityState.Modified; // Soft delete
                    break;
                case EntityState.Detached:
                case EntityState.Unchanged:
                default:
                    break;
            }
        }
        AddOutboxMessages();
        var result = await base.SaveChangesAsync(cancellationToken);
        return result;
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder); 
        
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        
        builder.Entity<AuditableEntity>()
            .Property(e => e.Version)
            .IsRowVersion();
    }

    private void AddOutboxMessages()
    {
        var messages = ChangeTracker.Entries<IHasDomainEvent>()
            .Select(c => c.Entity)
            .SelectMany(c =>
            {
                var domainEvents = new List<DomainEvent>(c.GetEvents());
                c.ClearEvents();
                return domainEvents;
            }).ToList();

        foreach (var message in messages)
        {
            Outboxes.Add(new OutboxEntity(Guid.NewGuid(), message));
        }
    }
}
