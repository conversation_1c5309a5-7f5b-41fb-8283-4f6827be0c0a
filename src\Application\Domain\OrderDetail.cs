﻿using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class OrderDetail : AuditableEntity
{
    public Guid Id { get; private init; }
    public required string Description { get; set; }
    public OrderDetailStatus Status { get; private set; }
    public required Iban Iban { get; set; }
    public decimal Amount { get; set; }

    public decimal WageAmount { get; set; }

    public CorrelationId WageTransferTransactionId { get; private set; }
    public bool WageTransactionStatus { get; set; }


    public Order Order { get; set; }
    public Guid OrderId { get; set; }
    public OrderDetailRollbackInfo? OrderDetailRollbackInfo { get; set; }
    public int? OrderDetailRollbackInfoId { get; set; }

    private OrderDetail() { }
    public static OrderDetail Create(Iban iban,
        decimal amount,
        decimal wageAmount,
        string? description = null)
    {
        var id = Guid.CreateVersion7();
        return new OrderDetail
        {
            Id = id,
            Description = description ?? $"آیتم - {id}",
            Status = OrderDetailStatus.Init,
            Iban = Iban.Of(iban),
            Amount = amount,
            WageAmount = wageAmount
        };
    }

    public void SetStatus(OrderDetailStatus status)
    {
        Status = status;
    }

    public void SetRollbackInfo(Guid rollbackDestinationWalletId)
    {
        OrderDetailRollbackInfo = OrderDetailRollbackInfo.Create(rollbackDestinationWalletId, Id);
    }
}

public enum OrderDetailStatus
{
    Init,
    InProgress,
    Success,
    Failed,
}