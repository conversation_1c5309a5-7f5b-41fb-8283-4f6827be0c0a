﻿using System.ComponentModel.DataAnnotations;

namespace Zify.Settlement.Application.Common;
public abstract class AuditableEntity: ISoftDeletable
{
    public DateTime Created { get; set; }
    public int? CreatedBy { get; set; }

    public DateTime? LastModified { get; set; }
    public int? LastModifiedBy { get; set; }
    
    public bool IsDeleted { get; set; }
    public DateTimeOffset? DeletedOn { get; set; }
    
    public byte[] Version { get; set; }
}

public interface ISoftDeletable
{
    public bool IsDeleted { get; set; }
    public DateTimeOffset? DeletedOn { get; set; }
}